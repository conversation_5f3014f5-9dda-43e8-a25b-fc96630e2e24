import logging
import time
from typing import Optional, List

from playwright.sync_api import Page, expect

from allm_helper.platforms.base_platform import BasePlatformDriver

logger = logging.getLogger(__name__)


class DeepSeekDriver(BasePlatformDriver):
    """
    DeepSeek Chat 平台的具体驱动实现。
    """
    CHAT_URL = "https://chat.deepseek.com/"

    def __init__(self, page: Page):
        """
        初始化 DeepSeek 驱动。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)
        # 等待页面加载完成
        try:
            # 如果跳转到登录页面，等待登录界面加载
            if "sign_in" in self.page.url:
                logger.info("检测到需要登录，请手动完成登录后继续...")
                self.page.wait_for_url("**/chat/**", timeout=300000)  # 等待5分钟用户登录
            # 等待聊天界面加载
            self.page.wait_for_selector("textarea", timeout=20000)
        except Exception:
            logger.warning("DeepSeek 页面加载超时或未找到输入区域。")

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话。
        """
        logger.info("正在 DeepSeek 中创建新会话...")
        try:
            # 查找"开启新对话"按钮
            new_chat_button = self.page.locator('text="开启新对话"').first
            if new_chat_button.is_visible():
                new_chat_button.click()
            else:
                # 尝试点击左上角的新建按钮
                plus_button = self.page.locator('img[cursor="pointer"]').first
                if plus_button.is_visible():
                    plus_button.click()
                else:
                    logger.info("未找到新建对话按钮，可能已经是新会话。")

            # 等待新会话页面加载完成，等待输入框出现
            expect(self.page.locator('textbox[placeholder*="给 DeepSeek 发送消息"]')).to_be_visible(timeout=10000)
            logger.info("已成功创建新会话。")
        except Exception as e:
            logger.error(f"创建新会话失败: {e}")
            # 如果失败，尝试通过刷新页面来恢复
            self.page.reload()
            self.page.wait_for_selector('textbox[placeholder*="给 DeepSeek 发送消息"]', timeout=20000)
        return self

    def use_existing_conversation(self, conversation_title: str):
        """
        切换到已有会话。
        """
        logger.warning(f"DeepSeek 驱动暂不支持按标题 '{conversation_title}' 切换会话。")
        pass

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。
        """
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")

        # 1. 上传附件 (如果需要)
        if attachments:
            logger.warning("DeepSeek 驱动暂不支持上传附件。")

        # 2. 输入提示并发送
        logger.info("正在发送提示...")
        try:
            # 查找输入框
            input_textarea = self.page.locator('textbox[placeholder*="给 DeepSeek 发送消息"]').first
            input_textarea.fill(prompt)

            # 查找发送按钮（通常是右下角的箭头按钮）
            send_button = self.page.locator('button:not([disabled])').last
            if send_button.is_visible():
                send_button.click()
            else:
                # 尝试按回车键发送
                input_textarea.press("Enter")

            logger.info("提示已发送。")
        except Exception as e:
            logger.error(f"发送提示失败: {e}")
            raise

        # 3. 等待回复完成
        logger.info("正在等待 DeepSeek 回复...")
        try:
            # 等待回复开始出现
            self.page.wait_for_timeout(2000)
            
            # 等待回复完成的标志
            # 通常是停止按钮消失或者出现复制按钮等
            for _ in range(60):  # 最多等待60秒
                # 检查是否有停止生成按钮
                stop_button = self.page.locator('button:has-text("停止"), button[aria-label*="停止"]').first
                if not stop_button.is_visible():
                    # 停止按钮消失，说明生成完成
                    break
                time.sleep(1)
            
            logger.info("DeepSeek 回复已接收。")
        except Exception as e:
            logger.error(f"等待回复超时或失败: {e}")
            pass
            
        # 4. 获取回复内容
        logger.info("正在提取回复内容...")
        try:
            # 查找最后一条AI回复（paragraph 元素包含回复内容）
            response_paragraph = self.page.locator('paragraph').last
            if response_paragraph.is_visible():
                full_response = response_paragraph.inner_text()
            else:
                # 备用方案：查找包含AI回复的其他可能元素
                ai_response = self.page.locator('img[ref="e243"] + paragraph, [role="assistant"]').last
                if ai_response.is_visible():
                    full_response = ai_response.inner_text()
                else:
                    full_response = "无法获取回复内容"
        except Exception as e:
            logger.error(f"提取回复内容失败: {e}")
            full_response = "提取回复失败"

        return full_response.strip()

    def is_ready(self) -> bool:
        """
        检查平台是否准备好接收新消息。
        """
        try:
            # 检查输入框是否可见且可用
            input_textarea = self.page.locator('textbox[placeholder*="给 DeepSeek 发送消息"]').first
            return input_textarea.is_visible() and input_textarea.is_enabled()
        except Exception:
            return False

    def save_chat(self, chat_name: str):
        """
        保存当前会话并命名。
        """
        logger.warning(f"DeepSeek 驱动暂不支持将会话保存为 '{chat_name}'。")
        pass

    def del_chat(self, chat_name: str):
        """
        删除指定名称的会话。
        """
        logger.warning(f"DeepSeek 驱动暂不支持删除名为 '{chat_name}' 的会话。")
        pass
