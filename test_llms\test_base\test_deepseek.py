import os
import pytest
import tempfile


@pytest.fixture
def deepseek_agent(chat_helper_factory, cfg):
    deepseek_agent = chat_helper_factory(
        platform_name="deepseek",
        user_email=cfg.deepseek.user_email,
        conversation_params={}
    )
    return deepseek_agent

def test_basic_chat(cfg, ctx, deepseek_agent):
    """测试基础聊天功能"""
    response = deepseek_agent.chat(cfg.deepseek.basic_chat_prompt)
    
    assert response is not None, "响应为空"
    assert len(response.strip()) > 0, "响应内容为空"
    assert cfg.deepseek.expected_keyword in response, f"响应中未包含期望关键词: {cfg.deepseek.expected_keyword}"
    
    ctx.basic_chat_response = response


def test_deep_thinking_feature(cfg, ctx, deepseek_agent):
    """测试深度思考功能"""
    response = deepseek_agent.chat(cfg.deepseek.deep_thinking_prompt)
    
    assert response is not None, "深度思考响应为空"
    assert len(response.strip()) > 0, "深度思考响应内容为空"
    
    ctx.deep_thinking_response = response


def test_code_generation(cfg, ctx, deepseek_agent):
    """测试代码生成功能"""
    response = deepseek_agent.chat(cfg.deepseek.code_generation_prompt)
    
    assert response is not None, "代码生成响应为空"
    assert len(response.strip()) > 0, "代码生成响应内容为空"
    assert "def" in response or "function" in response, "响应中未包含函数定义"
    
    ctx.code_generation_response = response


def test_new_conversation(cfg, ctx, deepseek_agent):
    """测试新建会话功能"""
    # 创建新会话
    deepseek_agent.driver.new_conversation()
    
    # 在新会话中测试聊天
    response = deepseek_agent.chat(cfg.deepseek.basic_chat_prompt)
    
    assert response is not None, "新会话响应为空"
    assert len(response.strip()) > 0, "新会话响应内容为空"
    
    ctx.new_conversation_response = response


def test_is_ready(cfg, ctx, deepseek_agent):
    """测试平台就绪状态检查"""
    is_ready = deepseek_agent.driver.is_ready()
    
    assert isinstance(is_ready, bool), "is_ready 应该返回布尔值"
    
    ctx.is_ready_status = is_ready


def test_continuous_conversation(cfg, ctx, deepseek_agent):
    """测试连续对话功能"""
    # 第一轮对话
    response1 = deepseek_agent.chat("我的名字是张三")
    assert response1 is not None, "第一轮对话响应为空"
    
    # 第二轮对话，测试上下文记忆
    response2 = deepseek_agent.chat("我的名字是什么？")
    assert response2 is not None, "第二轮对话响应为空"
    assert "张三" in response2, "AI未能记住之前的对话内容"
    
    ctx.continuous_conversation_responses = {
        "first": response1,
        "second": response2
    }


def test_error_handling(cfg, ctx, deepseek_agent):
    """测试错误处理"""
    # 测试空提示
    try:
        response = deepseek_agent.chat("")
        # 如果没有抛出异常，检查响应
        if response is not None:
            ctx.empty_prompt_response = response
    except Exception as e:
        ctx.empty_prompt_error = str(e)

    # 测试非常长的提示
    long_prompt = "测试" * 1000
    try:
        response = deepseek_agent.chat(long_prompt)
        if response is not None:
            ctx.long_prompt_response = response
    except Exception as e:
        ctx.long_prompt_error = str(e)


def test_feature_verification(cfg, ctx, deepseek_agent):
    """测试功能验证"""
    # 测试基础聊天功能
    response = deepseek_agent.chat(cfg.deepseek.basic_chat_prompt)
    
    assert response is not None, "功能验证测试响应为空"
    assert len(response.strip()) > 0, "功能验证测试响应内容为空"

    ctx.verification_results = {
        "basic_chat": response is not None and len(response.strip()) > 0,
        "platform_ready": deepseek_agent.driver.is_ready(),
        "response": response
    }


def test_verification_report(cfg, ctx):
    """生成功能验证报告"""
    if not hasattr(ctx, 'verification_results'):
        assert False, "需要先运行 test_feature_verification"

    results = ctx.verification_results

    # 生成报告
    report = []
    report.append("=== DeepSeek功能验证报告 ===")
    report.append(f"基础聊天功能: {'✓ 正常' if results['basic_chat'] else '✗ 异常'}")
    report.append(f"平台就绪状态: {'✓ 就绪' if results['platform_ready'] else '✗ 未就绪'}")
    report.append(f"响应内容: {'✓ 有效' if results['response'] else '✗ 无效'}")
    report.append("=" * 30)

    # 输出报告
    for line in report:
        print(line)

    # 至少基础聊天功能要正常
    assert results['basic_chat'], "基础聊天功能异常"
